#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
币种验证功能测试脚本
测试活跃永续合约获取和币种验证功能
"""

import sys
import time
import logging
from three_sniper_strategy import ThreeStateSniper

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_symbol_validation():
    """测试币种验证功能"""
    print("🔍 币种验证功能测试")
    print("=" * 50)
    
    # 测试配置
    config = {
        'api': {
            'api_key': 'nALsAPYzxprXmrgBRx7ZHIr1mnXlDqeZLoH3YL8hu8yu1Gpo65pHlJIDU6IlI20s',
            'api_secret': 'HNBVAQJ4LwlnIuULhCquEG1CQnvYVPo2e0SSkrH1XYMZFdBMEdjvXgrL2QlLEN4u'
        },
        'network': {
            'proxy': {
                'smart_proxy': True,
                'auto_detect': True,
                'target_server_ip': '*************',
                'enabled': False,
                'type': 'http',
                'host': '127.0.0.1',
                'port': 7897
            }
        }
    }
    
    try:
        # 创建策略实例
        print("\n1️⃣ 创建策略实例...")
        strategy = ThreeStateSniper(config)
        
        # 测试获取活跃永续合约
        print("\n2️⃣ 获取活跃永续合约列表...")
        active_symbols = strategy.get_active_futures_symbols()
        
        if active_symbols:
            print(f"✅ 发现 {len(active_symbols)} 个活跃永续合约")
            print("前10个币种:")
            for i, symbol in enumerate(active_symbols[:10], 1):
                print(f"  {i:2d}. {symbol}")
        else:
            print("❌ 未能获取活跃永续合约列表")
            return
        
        # 测试币种验证
        print("\n3️⃣ 测试币种验证功能...")
        test_symbols = [
            'BTCUSDT',      # 应该有效
            'ETHUSDT',      # 应该有效
            'ALPACAUSDT',   # 可能已下架
            'INVALIDUSDT',  # 无效币种
        ]
        
        for symbol in test_symbols:
            is_valid = strategy.validate_symbol(symbol)
            status = "✅ 有效" if is_valid else "❌ 无效"
            print(f"  {symbol:12} -> {status}")
        
        # 测试获取涨幅最高币种
        print("\n4️⃣ 获取涨幅最高的活跃币种...")
        top_symbols = strategy.get_top_symbols_by_change()
        
        if top_symbols:
            print(f"✅ 获取到 {len(top_symbols)} 个涨幅最高的币种:")
            for i, symbol in enumerate(top_symbols, 1):
                print(f"  {i:2d}. {symbol}")
        else:
            print("❌ 未能获取涨幅最高币种")
        
        # 测试流动性检查
        print("\n5️⃣ 测试流动性检查...")
        if top_symbols:
            for symbol in top_symbols[:5]:  # 测试前5个
                has_liquidity = strategy._check_symbol_liquidity(symbol)
                status = "💧 充足" if has_liquidity else "🏜️ 不足"
                print(f"  {symbol:12} -> 流动性 {status}")
        
        # 测试K线数据获取
        print("\n6️⃣ 测试K线数据获取...")
        if top_symbols:
            test_symbol = top_symbols[0]
            print(f"测试币种: {test_symbol}")
            
            df = strategy.fetch_klines_to_df(test_symbol)
            if df is not None:
                print(f"✅ 成功获取K线数据: {len(df)} 根K线")
                print(f"  最新价格: {df['close'].iloc[-1]:.4f}")
                print(f"  24小时成交量: {df['volume'].sum():.2f}")
            else:
                print("❌ 获取K线数据失败")
        
        # 测试币种健康检查
        print("\n7️⃣ 测试币种健康检查...")
        strategy.perform_symbol_health_check()
        
        print("\n✅ 币种验证测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_invalid_symbol_handling():
    """测试无效币种处理"""
    print("\n🗑️ 无效币种处理测试")
    print("=" * 30)
    
    config = {
        'api': {
            'api_key': 'nALsAPYzxprXmrgBRx7ZHIr1mnXlDqeZLoH3YL8hu8yu1Gpo65pHlJIDU6IlI20s',
            'api_secret': 'HNBVAQJ4LwlnIuULhCquEG1CQnvYVPo2e0SSkrH1XYMZFdBMEdjvXgrL2QlLEN4u'
        },
        'network': {'proxy': {'enabled': False}}
    }
    
    try:
        strategy = ThreeStateSniper(config)
        
        # 添加一些测试币种（包括无效的）
        strategy.top_symbols = [
            'BTCUSDT',      # 有效
            'ETHUSDT',      # 有效
            'ALPACAUSDT',   # 可能无效
            'INVALIDUSDT',  # 无效
            'TESTUSDT'      # 无效
        ]
        
        print("原始币种列表:")
        for symbol in strategy.top_symbols:
            print(f"  - {symbol}")
        
        # 执行健康检查
        print("\n执行健康检查...")
        strategy.perform_symbol_health_check()
        
        print("\n清理后的币种列表:")
        for symbol in strategy.top_symbols:
            print(f"  - {symbol}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    print("🎯 币种验证系统测试")
    print("=" * 60)
    
    # 主要功能测试
    test_symbol_validation()
    
    # 无效币种处理测试
    test_invalid_symbol_handling()
    
    print("\n🎉 所有测试完成！")
