#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Roll项目 - 代理管理模块
基于DevilRoll核心功能重构的代理配置和管理系统

遵循5S敏捷开发规则，实现标准化的网络代理管理
"""

import os
import json
import time
import logging
import socket
import requests
from pathlib import Path
from typing import Dict, Optional, List
from datetime import datetime, timedelta
import subprocess
import platform

logger = logging.getLogger(__name__)

class ProxyManager:
    """代理管理器"""
    
    def __init__(self, config: dict):
        self.config = config
        self.proxy_config = config.get('proxy', {})
        self.proxy_cache_file = Path("data/proxy_cache.json")
        
        # 代理配置
        self.enabled = self.proxy_config.get('enabled', False)
        self.type = self.proxy_config.get('type', 'http')  # http, https, socks5
        self.host = self.proxy_config.get('host', '127.0.0.1')
        self.port = self.proxy_config.get('port', 1080)
        self.username = self.proxy_config.get('username', '')
        self.password = self.proxy_config.get('password', '')
        
        # 缓存
        self.proxy_cache = self._load_proxy_cache()
        self.test_urls = [
            'https://api.binance.com/api/v3/time',
            'https://www.google.com',
            'https://httpbin.org/ip'
        ]
        
        logger.info("🔧 代理管理器初始化完成")
    
    def _load_proxy_cache(self) -> Dict[str, any]:
        """加载代理缓存"""
        if self.proxy_cache_file.exists():
            try:
                with open(self.proxy_cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"加载代理缓存失败: {e}")
        
        return {
            "last_test": None,
            "is_working": False,
            "response_times": [],
            "last_update": None,
            "environment": self._detect_environment()
        }
    
    def _save_proxy_cache(self) -> None:
        """保存代理缓存"""
        try:
            self.proxy_cache_file.parent.mkdir(parents=True, exist_ok=True)
            
            self.proxy_cache["last_update"] = datetime.now().isoformat()
            with open(self.proxy_cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.proxy_cache, f, indent=4, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"保存代理缓存失败: {e}")
    
    def _detect_environment(self) -> Dict[str, str]:
        """检测运行环境"""
        env_info = {
            "platform": platform.system(),
            "hostname": socket.gethostname(),
            "local_ip": self._get_local_ip(),
            "public_ip": None,
            "is_malaysia": False,
            "timestamp": datetime.now().isoformat()
        }
        
        # 获取公网IP
        public_ip = self._get_public_ip(direct=True)
        if public_ip:
            env_info["public_ip"] = public_ip
            env_info["is_malaysia"] = self._check_malaysia_ip(public_ip)
        
        return env_info
    
    def _get_local_ip(self) -> str:
        """获取本地IP"""
        try:
            # 创建一个UDP socket来获取本地IP
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.connect(('*******', 80))
            local_ip = sock.getsockname()[0]
            sock.close()
            return local_ip
        except Exception:
            return "127.0.0.1"
    
    def _get_public_ip(self, direct: bool = False) -> Optional[str]:
        """获取公网IP"""
        try:
            if direct or not self.enabled:
                # 直接连接获取IP
                response = requests.get('https://httpbin.org/ip', timeout=10)
                return response.json()['origin']
            else:
                # 通过代理获取IP
                proxies = self.get_requests_proxies()
                response = requests.get('https://httpbin.org/ip', 
                                      proxies=proxies, timeout=10)
                return response.json()['origin']
        except Exception as e:
            logger.warning(f"获取公网IP失败: {e}")
            return None
    
    def _check_malaysia_ip(self, ip: str) -> bool:
        """检查是否为马来西亚IP"""
        malaysia_ranges = [
            '*******/16',
            '********/15',
            '**********/15',
            '***********/13',
            '***********/15',
            '**********/16'
        ]
        
        # 简化的IP范围检查
        ip_parts = ip.split('.')
        if len(ip_parts) != 4:
            return False
        
        ip_num = (int(ip_parts[0]) << 24) + (int(ip_parts[1]) << 16) + \
                 (int(ip_parts[2]) << 8) + int(ip_parts[3])
        
        # 检查是否在马来西亚范围内
        malaysia_prefixes = [
            (0x01090000, 0xFFFF0000),  # *******/16
            (0x01200000, 0xFFFE0000),  # ********/15
            (0x0EC00000, 0xFFFE0000),  # **********/15
            (0xAF880000, 0xFFF80000),  # ***********/13
            (0xCABC0000, 0xFFFE0000),  # ***********/15
            (0xDB5F0000, 0xFFFF0000),  # **********/16
        ]
        
        for prefix, mask in malaysia_prefixes:
            if (ip_num & mask) == prefix:
                return True
        
        return False
    
    def test_binance_connection(self, use_proxy: bool = False) -> Dict[str, any]:
        """测试币安连接"""
        test_result = {
            "success": False,
            "response_time": None,
            "error": None,
            "ip": None,
            "timestamp": datetime.now().isoformat()
        }
        
        try:
            start_time = time.time()
            
            if use_proxy and self.enabled:
                proxies = self.get_requests_proxies()
                response = requests.get('https://api.binance.com/api/v3/time', 
                                      proxies=proxies, timeout=10)
            else:
                response = requests.get('https://api.binance.com/api/v3/time', timeout=10)
            
            response_time = (time.time() - start_time) * 1000
            
            test_result["success"] = response.status_code == 200
            test_result["response_time"] = round(response_time, 2)
            test_result["ip"] = self._get_public_ip(use_proxy)
            
            logger.info(f"✅ 币安连接测试成功: {response_time:.2f}ms")
            
        except Exception as e:
            test_result["error"] = str(e)
            logger.error(f"❌ 币安连接测试失败: {e}")
        
        return test_result
    
    def get_proxy_url(self) -> Optional[str]:
        """获取代理URL"""
        if not self.enabled:
            return None
        
        if self.username and self.password:
            return f"{self.type}://{self.username}:{self.password}@{self.host}:{self.port}"
        else:
            return f"{self.type}://{self.host}:{self.port}"
    
    def get_requests_proxies(self) -> Dict[str, str]:
        """获取requests代理配置"""
        if not self.enabled:
            return {}
        
        proxy_url = self.get_proxy_url()
        if not proxy_url:
            return {}
        
        return {
            'http': proxy_url,
            'https': proxy_url
        }
    
    def get_environment_info(self) -> Dict[str, any]:
        """获取环境信息"""
        return {
            "proxy_config": {
                "enabled": self.enabled,
                "type": self.type,
                "host": self.host,
                "port": self.port
            },
            "environment": self.proxy_cache.get("environment", {}),
            "last_test": self.proxy_cache.get("last_test"),
            "is_working": self.proxy_cache.get("is_working", False)
        }
    
    def run_connection_tests(self) -> Dict[str, any]:
        """运行连接测试"""
        results = {
            "direct_connection": self.test_binance_connection(use_proxy=False),
            "proxy_connection": None,
            "environment": self._detect_environment(),
            "recommendations": []
        }
        
        if self.enabled:
            results["proxy_connection"] = self.test_binance_connection(use_proxy=True)
            
            # 更新缓存
            self.proxy_cache["last_test"] = datetime.now().isoformat()
            self.proxy_cache["is_working"] = results["proxy_connection"]["success"]
            
            if results["proxy_connection"]["success"]:
                response_time = results["proxy_connection"]["response_time"]
                self.proxy_cache["response_times"].append(response_time)
                
                # 保留最近10次响应时间
                if len(self.proxy_cache["response_times"]) > 10:
                    self.proxy_cache["response_times"] = self.proxy_cache["response_times"][-10:]
            
            self._save_proxy_cache()
        
        # 生成建议
        results["recommendations"] = self._generate_recommendations(results)
        
        return results
    
    def _generate_recommendations(self, test_results: Dict) -> List[str]:
        """生成使用建议"""
        recommendations = []
        
        direct_ok = test_results["direct_connection"]["success"]
        proxy_ok = test_results["proxy_connection"]["success"] if test_results["proxy_connection"] else False
        
        if not direct_ok and not proxy_ok:
            recommendations.append("❌ 所有连接方式都失败，请检查网络设置")
        
        elif direct_ok and not proxy_ok:
            recommendations.append("✅ 直连正常，建议关闭代理")
        
        elif not direct_ok and proxy_ok:
            recommendations.append("✅ 代理有效，必须使用代理")
        
        elif direct_ok and proxy_ok:
            direct_time = test_results["direct_connection"]["response_time"]
            proxy_time = test_results["proxy_connection"]["response_time"]
            
            if proxy_time < direct_time * 1.5:
                recommendations.append("✅ 代理速度可接受，可以保持开启")
            else:
                recommendations.append("⚠️ 代理速度较慢，建议关闭")
        
        # 马来西亚环境特殊处理
        env = test_results["environment"]
        if env.get("is_malaysia", False):
            recommendations.append("⚠️ 检测到马来西亚IP，强烈建议使用代理")
        
        return recommendations
    
    def get_proxy_status(self) -> Dict[str, any]:
        """获取代理状态"""
        return {
            "enabled": self.enabled,
            "configured": bool(self.host and self.port),
            "working": self.proxy_cache.get("is_working", False),
            "last_test": self.proxy_cache.get("last_test"),
            "average_response_time": self._get_average_response_time()
        }
    
    def _get_average_response_time(self) -> Optional[float]:
        """获取平均响应时间"""
        response_times = self.proxy_cache.get("response_times", [])
        if response_times:
            return round(sum(response_times) / len(response_times), 2)
        return None
    
    def update_proxy_config(self, config: dict) -> bool:
        """更新代理配置"""
        try:
            self.proxy_config.update(config)
            
            # 更新实例变量
            self.enabled = self.proxy_config.get('enabled', False)
            self.type = self.proxy_config.get('type', 'http')
            self.host = self.proxy_config.get('host', '127.0.0.1')
            self.port = self.proxy_config.get('port', 1080)
            self.username = self.proxy_config.get('username', '')
            self.password = self.proxy_config.get('password', '')
            
            # 重新测试连接
            test_results = self.run_connection_tests()
            
            logger.info("✅ 代理配置已更新")
            return True
            
        except Exception as e:
            logger.error(f"更新代理配置失败: {e}")
            return False
    
    def export_proxy_report(self, filename: str = None) -> str:
        """导出代理报告"""
        if not filename:
            filename = f"reports/proxy_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = {
            "generated_at": datetime.now().isoformat(),
            "proxy_config": {
                "enabled": self.enabled,
                "type": self.type,
                "host": self.host,
                "port": self.port,
                "username_configured": bool(self.username),
                "password_configured": bool(self.password)
            },
            "environment": self.get_environment_info(),
            "status": self.get_proxy_status(),
            "test_results": self.run_connection_tests()
        }
        
        # 保存报告
        report_path = Path(filename)
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=4, ensure_ascii=False)
        
        logger.info(f"📄 代理报告已导出: {report_path}")
        return str(report_path)

# 快速使用函数
def create_proxy_manager(config: dict) -> ProxyManager:
    """创建代理管理器"""
    return ProxyManager(config)

def test_network_connectivity(config: dict) -> Dict[str, any]:
    """测试网络连接"""
    manager = ProxyManager(config)
    return manager.run_connection_tests()

def get_network_recommendations(config: dict) -> List[str]:
    """获取网络使用建议"""
    manager = ProxyManager(config)
    results = manager.run_connection_tests()
    return results["recommendations"]

# 导出类
__all__ = [
    'ProxyManager',
    'create_proxy_manager',
    'test_network_connectivity',
    'get_network_recommendations'
]